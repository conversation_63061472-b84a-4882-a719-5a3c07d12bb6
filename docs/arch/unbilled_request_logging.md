# Unbilled Request Logging Implementation

## Overview

This document describes the comprehensive error logging system implemented to ensure that any requests sent to upstream adaptors/channels that haven't been properly billed are logged as ERROR. This is critical for tracking potential revenue loss and ensuring billing integrity.

## Problem Statement

The user requested a review of all code to ensure that any requests sent to upstream adaptors or channels that haven't been billed are logged as ERROR. This is essential for:

1. **Revenue Protection**: Detecting requests that were processed but not billed
2. **System Integrity**: Ensuring billing operations complete successfully
3. **Audit Trail**: Maintaining comprehensive logs for financial tracking
4. **Debugging**: Identifying billing system failures

## Implementation Strategy

### 1. Upstream Request Logging

**File**: `relay/adaptor/common.go`

Added comprehensive logging for all upstream requests through the `DoRequestHelper` function:

```go
// Log upstream request for billing tracking
logger.Logger.Info("sending request to upstream channel",
    zap.String("url", fullRequestURL),
    zap.Int("channelId", meta.ChannelId),
    zap.Int("userId", meta.UserId),
    zap.String("model", meta.ActualModelName),
    zap.String("channelName", a.GetChannelName()))

// Log failed upstream request as ERROR for billing tracking
logger.Logger.Error("upstream request failed - potential unbilled request",
    zap.Error(err),
    zap.String("url", fullRequestURL),
    zap.Int("channelId", meta.ChannelId),
    zap.Int("userId", meta.UserId),
    zap.String("model", meta.ActualModelName),
    zap.String("channelName", a.GetChannelName()))
```

### 2. Audio API Direct Request Logging

**File**: `relay/controller/audio.go`

Added similar logging for the Audio API's direct HTTP requests:

```go
// Log upstream request for billing tracking
logger.Logger.Info("sending audio request to upstream channel",
    zap.String("url", fullRequestURL),
    zap.Int("channelId", channelId),
    zap.Int("userId", userId),
    zap.String("model", audioModel),
    zap.Int("relayMode", relayMode))

// Log failed upstream request as ERROR for billing tracking
logger.Logger.Error("upstream audio request failed - potential unbilled request",
    zap.Error(err),
    zap.String("url", fullRequestURL),
    zap.Int("channelId", channelId),
    zap.Int("userId", userId),
    zap.String("model", audioModel),
    zap.Int("relayMode", relayMode))
```

### 3. Critical Billing Operation Error Logging

**File**: `relay/billing/billing.go`

Enhanced error logging for critical billing operations:

#### PostConsumeTokenQuota Failures
```go
logger.Logger.Error("CRITICAL: upstream request was sent but billing failed - unbilled request detected",
    zap.Error(err),
    zap.Int("tokenId", tokenId),
    zap.Int("userId", userId),
    zap.Int("channelId", channelId),
    zap.String("model", modelName),
    zap.Int64("quotaDelta", quotaDelta),
    zap.Int64("totalQuota", totalQuota))
```

#### User Quota Cache Update Failures
```go
logger.Logger.Error("CRITICAL: upstream request was sent but user quota cache update failed",
    zap.Error(err),
    zap.Int("userId", userId),
    zap.Int("channelId", channelId),
    zap.String("model", modelName),
    zap.Int64("totalQuota", totalQuota))
```

#### Pre-Consumed Quota Refund Failures
```go
logger.Logger.Error("CRITICAL: failed to return pre-consumed quota - potential double billing",
    zap.Error(err),
    zap.Int("tokenId", tokenId),
    zap.Int64("preConsumedQuota", preConsumedQuota))
```

### 4. Database Operation Error Logging

**File**: `model/log.go`

Enhanced billing log recording failures:

```go
if log.Type == LogTypeConsume {
    logger.Logger.Error("CRITICAL: failed to record billing log - upstream request sent but not logged",
        zap.Error(err),
        zap.Int("userId", log.UserId),
        zap.Int("channelId", log.ChannelId),
        zap.String("model", log.ModelName),
        zap.Int("quota", log.Quota),
        zap.String("requestId", log.RequestId))
}
```

**File**: `model/user.go`

Enhanced user quota update failures:

```go
logger.Logger.Error("CRITICAL: failed to update user used quota and request count - upstream request sent but user quota not updated",
    zap.Error(err),
    zap.Int("userId", id),
    zap.Int64("quota", quota),
    zap.Int("count", count))
```

**File**: `model/channel.go`

Enhanced channel quota update failures:

```go
logger.Logger.Error("CRITICAL: failed to update channel used quota - upstream request sent but channel quota not updated",
    zap.Error(err),
    zap.Int("channelId", id),
    zap.Int64("quota", quota))
```

## Coverage Analysis

### ✅ **Fully Covered Relay Helpers**

1. **RelayTextHelper** - Complete billing with comprehensive error logging
2. **RelayImageHelper** - Complete billing with comprehensive error logging
3. **RelayAudioHelper** - Complete billing with comprehensive error logging
4. **RelayResponseAPIHelper** - Complete billing with comprehensive error logging
5. **RelayClaudeMessagesHelper** - Complete billing with comprehensive error logging
6. **RelayProxyHelper** - Intentionally logs with 0 quota (free proxy requests)

### 🔍 **Error Scenarios Covered**

1. **Upstream Request Failures**: All DoRequest failures are logged as ERROR
2. **Billing Database Failures**: PostConsumeTokenQuota failures are logged as CRITICAL
3. **Cache Update Failures**: User quota cache update failures are logged as CRITICAL
4. **Log Recording Failures**: Billing log recording failures are logged as CRITICAL
5. **User Quota Update Failures**: User quota database update failures are logged as CRITICAL
6. **Channel Quota Update Failures**: Channel quota database update failures are logged as CRITICAL
7. **Quota Refund Failures**: Pre-consumed quota refund failures are logged as CRITICAL

## Log Levels and Keywords

### INFO Level
- `"sending request to upstream channel"` - Normal upstream request tracking
- `"sending audio request to upstream channel"` - Normal audio request tracking

### ERROR Level
- `"upstream request failed - potential unbilled request"` - Failed upstream requests
- `"upstream audio request failed - potential unbilled request"` - Failed audio requests

### CRITICAL Level (ERROR with CRITICAL prefix)
- `"CRITICAL: upstream request was sent but billing failed - unbilled request detected"`
- `"CRITICAL: upstream request was sent but user quota cache update failed"`
- `"CRITICAL: failed to record billing log - upstream request sent but not logged"`
- `"CRITICAL: failed to update user used quota and request count - upstream request sent but user quota not updated"`
- `"CRITICAL: failed to update channel used quota - upstream request sent but channel quota not updated"`
- `"CRITICAL: failed to return pre-consumed quota - potential double billing"`

## Monitoring and Alerting

### Recommended Alert Rules

1. **Unbilled Request Detection**:
   ```
   Alert on: "CRITICAL: upstream request was sent but billing failed"
   Severity: High
   Action: Immediate investigation required
   ```

2. **Billing Log Failures**:
   ```
   Alert on: "CRITICAL: failed to record billing log"
   Severity: High
   Action: Check database connectivity and disk space
   ```

3. **Quota Update Failures**:
   ```
   Alert on: "CRITICAL: failed to update.*quota"
   Severity: Medium
   Action: Check database performance and connectivity
   ```

## Testing

All existing tests continue to pass, confirming that the logging enhancements don't break existing functionality:

- ✅ `relay/controller` tests pass
- ✅ `relay/billing` tests pass
- ✅ No regression in billing logic
- ✅ Enhanced error visibility maintained

## Benefits

1. **Complete Visibility**: Every upstream request is now logged
2. **Revenue Protection**: Critical billing failures are immediately visible
3. **Audit Compliance**: Comprehensive trail of all billing operations
4. **Debugging Support**: Detailed context for troubleshooting billing issues
5. **Proactive Monitoring**: Clear keywords for automated alerting

## Conclusion

This implementation ensures that no upstream request can go untracked. Any request sent to upstream adaptors/channels that fails to be properly billed will be logged as ERROR or CRITICAL, providing complete visibility into potential revenue loss and system integrity issues.
