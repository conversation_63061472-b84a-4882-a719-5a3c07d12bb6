package main

import (
	"context"
	"os"
	"time"

	"github.com/Laisky/zap"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
)

func main() {
	ctx := context.Background()

	// Example 1: Setup with environment variables
	// You can set these before running the program:
	// export LOG_PUSH_API="https://your-webhook-url.com/api/alerts"
	// export LOG_PUSH_TYPE="webhook"
	// export LOG_PUSH_TOKEN="your-secret-token"

	// Example 2: Setup programmatically (for testing)
	if os.Getenv("LOG_PUSH_API") == "" {
		// Set test configuration - replace with your actual webhook URL
		config.LogPushAPI = "https://httpbin.org/post" // This is a test endpoint
		config.LogPushType = "webhook"
		config.LogPushToken = "test-token"
	}

	// Initialize the enhanced logger
	logger.SetupEnhancedLogger(ctx)

	// Example log messages
	logger.Logger.Info("Application started with alert pusher integration")

	// This will NOT trigger an alert (info level)
	logger.Logger.Info("Processing user request",
		zap.String("user_id", "12345"),
		zap.String("action", "login"))

	// This will NOT trigger an alert (warn level)
	logger.Logger.Warn("Rate limit approaching",
		zap.String("user_id", "12345"),
		zap.Int("requests", 95),
		zap.Int("limit", 100))

	// This WILL trigger an alert (error level)
	logger.Logger.Error("Database connection failed",
		zap.String("component", "database"),
		zap.String("error_type", "connection_error"),
		zap.String("database", "postgresql"),
		zap.Duration("timeout", 30*time.Second))

	// Another error that will trigger an alert
	logger.Logger.Error("API request failed",
		zap.String("component", "api_client"),
		zap.String("error_type", "http_error"),
		zap.String("endpoint", "/api/v1/users"),
		zap.Int("status_code", 500),
		zap.String("method", "GET"))

	// Wait a moment to allow alerts to be sent
	time.Sleep(2 * time.Second)

	logger.Logger.Info("Example completed - check your alert system for notifications")
}
